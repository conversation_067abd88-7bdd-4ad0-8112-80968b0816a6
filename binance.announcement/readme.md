现有 get.announcement.js 脚本逻辑如下：

const fs = require('fs').promises;
const path = require('path');
const cheerio = require('cheerio');
const url = `https://www.binance.com/en/support/announcement`;
const latestArticle = {
  "id": 244903,
  "code": "4254f23cffd6477496c19135d3f1e3be",
  "title": "Participate in the Exclusive OpenEden (EDEN) Booster Campaign on Binance Wallet",
  "imageLink": null,
  "shortLink": null,
  "body": null,
  "type": 1,
  "catalogId": 93,
  "catalogName": "Latest Activities",
  "publishDate": 1755262801102,
  "footer": null
};
async function main() {
  const start = performance.now();
  const response = await fetch("https://www.binance.com/en/support/announcement", {
    "headers": {
      "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
      "cache-control": "no-cache",
      "pragma": "no-cache",
      "priority": "u=0, i",
      "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "\"macOS\"",
      "sec-fetch-dest": "document",
      "sec-fetch-mode": "navigate",
      "sec-fetch-site": "same-origin",
      "upgrade-insecure-requests": "1",
      "cookie": "aws-waf-token=f5b7d574-f9e0-4e16-8dd5-5c94d2425fd9:BAoAunoVK9cEAAAA:+BFOPdg5CsbrB2dD0u485GRDMZS6OKWiWhTX6MDtKOBnOo4KYY7FvmapsiuM4MEAIzX+T0hjHs2Qm+RSbIveIHsUCnZqQQhDJB7aeWD48xrPUI6tg/JN9CUeSRjd2zfdKpSwSVvNiMSwA0CgfLibgf/lRMc+ieZ25sB/WTr533342CkjRS0TsCcEtZS6xQku2WQ=",
      "Referer": "https://www.binance.com/en/support/announcement"
    },
    "body": null,
    "method": "GET"
  });

  const html = await response.text();
  await fs.writeFile(path.resolve(__dirname, 'announcement.html'), html);
  console.log('save announcement.html done');

  const $ = cheerio.load(html);
  const script = $('#__APP_DATA').text();
  const data = JSON.parse(script);
  const dataByRouteId = data?.appState?.loader?.dataByRouteId;
  const latestArticles = [];
  const listing = [];
  for (prop in dataByRouteId) {
    const keyProp = dataByRouteId[prop];
    if (keyProp?.latestArticles) {
      latestArticles.push(...keyProp.latestArticles);
      const tmp = keyProp.latestArticles.filter(item => item.catalogName === 'New Cryptocurrency Listing');
      listing.push(...tmp);
      console.log('add listing', tmp?.map(item => item.title));
    }
  }
  await fs.writeFile(path.resolve(__dirname, 'listing.json'), JSON.stringify(listing, null, 2));
  await fs.writeFile(path.resolve(__dirname, 'announcement.json'), JSON.stringify(latestArticles, null, 2));
  const end = performance.now();
  console.log('time:', end - start);
}
main().catch(console.error);


这个脚本的目的是实时抓取 binance 最新的公告，特别是 Listing 公告。如果是第一时间抓取到公告信息，那么我就有机会通过这个消息进行交易获利，但是前提是我必须第一时间，非常快的获取到 binance 的公告信息。

我希望将这个脚本改造成“企业级消息驱动交易”脚本。

请帮我做出对应的修改。
不需要有交易逻辑。这个脚本专注的是“极致的速度第一时间获取到公告信息”。
间隔时间越短越好，最好是 ms 级别。
尽可能做到代码的极致优化：能用 js 实现的最高性能天花板。