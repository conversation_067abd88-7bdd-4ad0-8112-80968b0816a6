const { BinanceAnnouncementMonitor } = require('./get.announcement');
const { logger } = require('./logger');

async function testMonitor() {
  console.log('🧪 测试监控器运行...');
  logger.info('Testing monitor execution');

  const monitor = new BinanceAnnouncementMonitor({ 
    interval: 5000,
    timeout: 3000,
    concurrency: 1
  });

  // 监听事件
  monitor.on('started', () => {
    console.log('✅ 监控器启动成功');
    logger.info('Monitor started successfully');
  });

  monitor.on('error', (error) => {
    console.log(`⚠️  监控器错误: ${error.message}`);
    logger.error('Monitor error', { error: error.message });
  });

  monitor.on('cookieRefreshFailed', (error) => {
    console.log(`🍪 Cookie 刷新失败: ${error.message}`);
    logger.error('<PERSON>ie refresh failed', { error: error.message });
  });

  monitor.on('newAnnouncements', (articles) => {
    console.log(`🔔 检测到 ${articles.length} 个新公告`);
    logger.info('New announcements detected', { count: articles.length });
  });

  try {
    console.log('🚀 启动监控器...');
    await monitor.start();
    
    console.log('⏳ 运行 10 秒测试...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    console.log('🛑 停止监控器...');
    await monitor.stop();
    
    console.log('✅ 测试完成');
    logger.info('Monitor test completed successfully');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    logger.error('Monitor test failed', { 
      error: error.message,
      stack: error.stack 
    });
  } finally {
    await logger.cleanup();
  }
}

testMonitor().catch(console.error);
