const { <PERSON><PERSON>Manager } = require('./cookie-manager');
const { logger } = require('./logger');

/**
 * 测试 Cookie 管理器功能
 */
async function testCookieManager() {
  console.log('🧪 开始测试 Cookie 管理器...');
  logger.info('Starting Cookie Manager Test');

  const cookieManager = new CookieManager({
    timeout: 30000,
    retryAttempts: 2,
    retryDelay: 3000,
    headless: true // 无头模式测试
  });

  try {
    // 测试 1: 获取有效 Cookie
    console.log('\n📋 测试 1: 获取有效 Cookie');
    logger.info('Test 1: Getting valid cookies');
    
    const startTime = Date.now();
    const cookies = await cookieManager.getValidCookies();
    const duration = Date.now() - startTime;
    
    console.log(`✅ Cookie 获取成功! 长度: ${cookies.length} 字符`);
    console.log(`⏱️  耗时: ${duration}ms`);
    logger.info('Test 1 completed successfully', {
      cookieLength: cookies.length,
      duration: duration
    });

    // 测试 2: 验证 Cookie 格式
    console.log('\n📋 测试 2: 验证 Cookie 格式');
    logger.info('Test 2: Validating cookie format');
    
    const cookieParts = cookies.split(';');
    console.log(`🍪 Cookie 包含 ${cookieParts.length} 个部分`);
    
    // 检查关键 cookie
    const hasAwsWafToken = cookies.includes('aws-waf-token=');
    const hasBncUuid = cookies.includes('bnc-uuid=');
    
    console.log(`🔍 包含 aws-waf-token: ${hasAwsWafToken}`);
    console.log(`🔍 包含 bnc-uuid: ${hasBncUuid}`);
    
    if (hasAwsWafToken && hasBncUuid) {
      console.log('✅ Cookie 格式验证通过');
      logger.info('Test 2 completed successfully', {
        cookieParts: cookieParts.length,
        hasAwsWafToken,
        hasBncUuid
      });
    } else {
      console.log('⚠️  Cookie 可能缺少关键部分');
      logger.warn('Test 2 warning: Cookie may be missing key parts', {
        hasAwsWafToken,
        hasBncUuid
      });
    }

    // 测试 3: 验证 Cookie 有效性
    console.log('\n📋 测试 3: 验证 Cookie 有效性');
    logger.info('Test 3: Validating cookie effectiveness');
    
    const validationStartTime = Date.now();
    const isValid = await cookieManager.validateCookies(cookies);
    const validationDuration = Date.now() - validationStartTime;
    
    console.log(`🔍 Cookie 有效性: ${isValid ? '有效' : '无效'}`);
    console.log(`⏱️  验证耗时: ${validationDuration}ms`);
    
    if (isValid) {
      console.log('✅ Cookie 有效性验证通过');
      logger.info('Test 3 completed successfully', {
        isValid,
        validationDuration
      });
    } else {
      console.log('❌ Cookie 有效性验证失败');
      logger.error('Test 3 failed: Cookie validation failed', {
        isValid,
        validationDuration
      });
    }

    console.log('\n🎉 所有测试完成!');
    logger.info('All Cookie Manager tests completed');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    logger.error('Cookie Manager test failed', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  } finally {
    // 清理资源
    await cookieManager.cleanup();
    console.log('🧹 资源清理完成');
    logger.info('Cookie Manager test cleanup completed');
  }
}

/**
 * 测试主程序的错误恢复功能
 */
async function testErrorRecovery() {
  console.log('\n🧪 开始测试错误恢复功能...');
  logger.info('Starting Error Recovery Test');

  const { BinanceAnnouncementMonitor } = require('./get.announcement');
  
  const monitor = new BinanceAnnouncementMonitor({
    interval: 5000, // 5秒间隔，便于测试
    timeout: 3000,
    concurrency: 1,
    cookieRefreshInterval: 10000 // 10秒刷新间隔，便于测试
  });

  // 监听事件
  monitor.on('cookieRefreshed', (info) => {
    console.log(`🍪 测试中检测到 Cookie 刷新: 第${info.refreshCount}次`);
    logger.info('Test detected cookie refresh', info);
  });

  monitor.on('cookieExpired', (error) => {
    console.log(`🍪 测试中检测到 Cookie 过期: ${error.message}`);
    logger.info('Test detected cookie expiration', { error: error.message });
  });

  monitor.on('error', (error) => {
    console.log(`❌ 测试中检测到错误: ${error.message}`);
    logger.error('Test detected error', { error: error.message });
  });

  try {
    console.log('🚀 启动监控器进行短期测试...');
    await monitor.start();
    
    // 运行 30 秒测试
    console.log('⏳ 运行 30 秒测试...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    console.log('🛑 停止监控器...');
    await monitor.stop();
    
    console.log('✅ 错误恢复测试完成');
    logger.info('Error recovery test completed successfully');

  } catch (error) {
    console.error('❌ 错误恢复测试失败:', error.message);
    logger.error('Error recovery test failed', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// 主测试函数
async function runTests() {
  console.log('🎯 开始运行所有测试...\n');
  logger.info('Starting all tests');

  try {
    // 测试 Cookie 管理器
    await testCookieManager();
    
    // 测试错误恢复（可选，因为会运行实际监控）
    const runErrorRecoveryTest = process.argv.includes('--full-test');
    if (runErrorRecoveryTest) {
      await testErrorRecovery();
    } else {
      console.log('\n💡 提示: 使用 --full-test 参数运行完整测试（包括错误恢复测试）');
    }

    console.log('\n🎉 所有测试成功完成!');
    logger.info('All tests completed successfully');

  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    logger.error('Tests failed', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  } finally {
    // 清理日志
    try {
      await logger.cleanup();
    } catch (error) {
      console.error('日志清理失败:', error.message);
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testCookieManager,
  testErrorRecovery,
  runTests
};
