请帮我阅读源码 @/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js 。
这段 js 的作用是通过本地的 cookie 持续的获取币安的公告以监控公告最新消息。
由于公告 url 做了简单的鉴权，需要 cookie 才能正常访问 然后解析出 __APP_DATA 进行后续流程。
现在需要你做的是：
1. 如果找不到 __APP_DATA 标签，无法解析出 JSON 数据的时候，那么重新获取 cookie，然后再重新执行程序。
2. 获取 cookie 的逻辑：调研 puppeteer 或者 playwright 等无头浏览器的库，尽可能的模拟客户端，访问公告 URL，然后将返回的 cookie 保存到本地（可能访问公告 URL 之后会有多次重定向）

------------------------------------------------------------------------------------




