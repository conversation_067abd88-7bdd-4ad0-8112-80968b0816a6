[2025-08-26 06:33:04.665] [ERROR] <PERSON><PERSON> refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:673:3)"}
[2025-08-26 06:33:04.669] [INFO] Cookie refresh started
[2025-08-26 06:33:04.670] [ERROR] Cookie refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:33:11.845] [ERROR] Cookie refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:685:3)"}
[2025-08-26 06:33:11.849] [INFO] Cookie refresh started
[2025-08-26 06:33:11.851] [ERROR] Cookie refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:38:39.410] [INFO] Existing cookie format appears valid {"cookieLength":1399,"cookieParts":10,"hasAwsWafToken":true,"hasBncUuid":true}
[2025-08-26 06:38:39.411] [INFO] All basic tests completed successfully
[2025-08-26 06:39:12.095] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:39:12.095] [ERROR] 🍪 Cookie 刷新失败:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:39:12.213] [ERROR] 🚀 启动错误:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:39:12.214] [INFO] Cookie refresh started
[2025-08-26 06:39:35.052] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:39:35.052] [ERROR] 🍪 Cookie 刷新失败:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:39:37.174] [INFO] 🔄 Graceful shutdown initiated
[2025-08-26 06:39:37.174] [INFO] ✅ Monitor stopped successfully
[2025-08-26 06:39:39.471] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)"}
[2025-08-26 06:39:39.583] [INFO] Cookie refresh started
[2025-08-26 06:39:44.584] [INFO] Cookie refresh started
[2025-08-26 06:40:33.305] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async testMonitor (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-monitor.js:37:5)"}
[2025-08-26 06:40:33.305] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: "}
[2025-08-26 06:40:33.390] [INFO] Monitor started successfully
[2025-08-26 06:40:33.390] [INFO] Cookie refresh started
[2025-08-26 06:40:43.509] [INFO] Monitor test completed successfully
[2025-08-26 06:40:57.367] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:40:57.367] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: "}
[2025-08-26 06:42:08.736] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:42:08.737] [ERROR] 🍪 Cookie 刷新失败:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:42:08.853] [ERROR] 🚀 启动错误:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:42:08.853] [INFO] Cookie refresh started
