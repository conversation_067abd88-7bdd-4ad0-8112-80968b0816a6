[2025-08-26 06:33:04.665] [ERROR] <PERSON><PERSON> refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:673:3)"}
[2025-08-26 06:33:04.669] [INFO] Cookie refresh started
[2025-08-26 06:33:04.670] [ERROR] Cookie refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:33:11.845] [ERROR] Cookie refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:685:3)"}
[2025-08-26 06:33:11.849] [INFO] Cookie refresh started
[2025-08-26 06:33:11.851] [ERROR] Cookie refresh failed {"error":"浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.","stack":"Error: 浏览器启动失败: Could not find Chrome (ver. 139.0.7258.138). This can occur if either\n 1. you did not perform an installation before running the script (e.g. `npx puppeteer browsers install chrome`) or\n 2. your cache path is incorrectly configured (which is: /Users/<USER>/.cache/puppeteer).\nFor (2), check out our guide on configuring puppeteer at https://pptr.dev/guides/configuration.\n    at CookieManager.initBrowser (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:61:13)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:278:7)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:38:39.410] [INFO] Existing cookie format appears valid {"cookieLength":1399,"cookieParts":10,"hasAwsWafToken":true,"hasBncUuid":true}
[2025-08-26 06:38:39.411] [INFO] All basic tests completed successfully
[2025-08-26 06:39:12.095] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:39:12.095] [ERROR] 🍪 Cookie 刷新失败:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:39:12.213] [ERROR] 🚀 启动错误:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:39:12.214] [INFO] Cookie refresh started
[2025-08-26 06:39:35.052] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:39:35.052] [ERROR] 🍪 Cookie 刷新失败:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:39:37.174] [INFO] 🔄 Graceful shutdown initiated
[2025-08-26 06:39:37.174] [INFO] ✅ Monitor stopped successfully
[2025-08-26 06:39:39.471] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)"}
[2025-08-26 06:39:39.583] [INFO] Cookie refresh started
[2025-08-26 06:39:44.584] [INFO] Cookie refresh started
[2025-08-26 06:40:33.305] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async testMonitor (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-monitor.js:37:5)"}
[2025-08-26 06:40:33.305] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: "}
[2025-08-26 06:40:33.390] [INFO] Monitor started successfully
[2025-08-26 06:40:33.390] [INFO] Cookie refresh started
[2025-08-26 06:40:43.509] [INFO] Monitor test completed successfully
[2025-08-26 06:40:57.367] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:40:57.367] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: "}
[2025-08-26 06:42:08.736] [ERROR] Cookie refresh failed {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:42:08.737] [ERROR] 🍪 Cookie 刷新失败:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:42:08.853] [ERROR] 🚀 启动错误:获取 cookie 失败，已重试 3 次。最后错误: HTTP 405:  {"error":"获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: ","stack":"Error: 获取 cookie 失败，已重试 3 次。最后错误: HTTP 405: \n    at CookieManager.fetchCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:179:11)\n    at async CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:292:20)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async main (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:738:3)"}
[2025-08-26 06:42:08.853] [INFO] Cookie refresh started
[2025-08-26 06:51:16.262] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:443:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async testMonitor (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-monitor.js:37:5)"}
[2025-08-26 06:51:16.262] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:16.263] [INFO] Monitor started successfully
[2025-08-26 06:51:16.263] [INFO] Cookie refresh started
[2025-08-26 06:51:16.279] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:443:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:51:16.279] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:39.044] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async testMonitor (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-monitor.js:37:5)"}
[2025-08-26 06:51:39.044] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:39.045] [INFO] Monitor started successfully
[2025-08-26 06:51:39.045] [INFO] Cookie refresh started
[2025-08-26 06:51:39.045] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:51:39.045] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:44.045] [INFO] Cookie refresh started
[2025-08-26 06:51:44.046] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:51:44.046] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:49.045] [INFO] Monitor test completed successfully
[2025-08-26 06:51:53.524] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async testMonitor (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-monitor.js:37:5)"}
[2025-08-26 06:51:53.525] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:53.525] [INFO] Monitor started successfully
[2025-08-26 06:51:53.525] [INFO] Cookie refresh started
[2025-08-26 06:51:53.525] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:51:53.525] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:57.476] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.start (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:508:7)\n    at async testMonitor (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-monitor.js:37:5)"}
[2025-08-26 06:51:57.476] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:51:57.476] [INFO] Monitor started successfully
[2025-08-26 06:51:57.476] [INFO] Cookie refresh started
[2025-08-26 06:51:57.477] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async BinanceAnnouncementMonitor.refreshCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:299:25)\n    at async BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:455:9)"}
[2025-08-26 06:51:57.477] [ERROR] Cookie refresh failed {"error":"this.initBrowser is not a function"}
[2025-08-26 06:52:35.943] [ERROR] Playwright Cookie Manager test failed {"error":"this.initBrowser is not a function","stack":"TypeError: this.initBrowser is not a function\n    at CookieManager.getValidCookies (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/cookie-manager.js:444:18)\n    at async testPlaywrightCookieManager (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-playwright-cookie.js:31:21)\n    at async runPlaywrightTests (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/test-playwright-cookie.js:230:25)"}
[2025-08-26 06:52:35.943] [INFO] Test cleanup completed
[2025-08-26 06:53:21.081] [INFO] Test 1 completed successfully {"cookieLength":356,"duration":7153,"successfulStrategies":{"chromium-stealth":1}}
[2025-08-26 06:53:21.081] [INFO] Test 2: Cookie format validation
[2025-08-26 06:53:21.081] [WARN] Test 2 warning: Cookie quality may be insufficient {"hasAwsWafToken":true,"hasBncUuid":false,"hasSessionId":false,"qualityScore":1}
[2025-08-26 06:53:21.081] [INFO] Test 3: Cookie effectiveness validation
[2025-08-26 06:53:21.182] [ERROR] Test 3 failed: Cookie validation failed {"isValid":false,"validationDuration":101}
[2025-08-26 06:53:21.182] [INFO] Test 4: Strategy performance analysis
[2025-08-26 06:53:21.182] [INFO] Test 4 completed {"successfulStrategies":{"chromium-stealth":1},"failedStrategies":[]}
[2025-08-26 06:53:21.182] [INFO] All Playwright Cookie Manager tests completed successfully
[2025-08-26 06:53:21.182] [INFO] Test cleanup completed
[2025-08-26 06:53:21.182] [INFO] All Playwright tests completed successfully
[2025-08-26 06:53:28.589] [INFO] Cookie refresh completed successfully {"refreshCount":1,"cookieLength":356}
[2025-08-26 06:53:28.589] [INFO] Monitor started successfully
[2025-08-26 06:53:28.881] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:53:28.882] [ERROR] Monitor error {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:53:28.882] [INFO] Cookie refresh started
[2025-08-26 06:53:34.472] [INFO] Cookie refresh completed successfully {"refreshCount":2,"cookieLength":356}
[2025-08-26 06:53:34.824] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:53:34.824] [ERROR] Monitor error {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:53:38.590] [INFO] Monitor test completed successfully
[2025-08-26 06:54:29.313] [INFO] Cookie refresh completed successfully {"refreshCount":1,"cookieLength":356}
[2025-08-26 06:54:29.313] [INFO] 🍪 Cookie 刷新成功 (第1次) - 2025-08-26T06:54:29.313Z {"timestamp":1756191269313,"refreshCount":1}
[2025-08-26 06:54:29.603] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:54:29.603] [WARN] 🍪 Cookie 过期检测:Could not find __APP_DATA script tag - Cookie may be expired {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:54:29.604] [ERROR] ❌ Monitor Error:Could not find __APP_DATA script tag - Cookie may be expired {"error":"Could not find __APP_DATA script tag - Cookie may be expired","type":"COOKIE_EXPIRED","stack":"Error: Could not find __APP_DATA script tag - Cookie may be expired\n    at BinanceAnnouncementMonitor.parseAnnouncements (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:243:23)\n    at BinanceAnnouncementMonitor.processResults (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:373:55)\n    at BinanceAnnouncementMonitor.executeRequest (/Users/<USER>/Desktop/conan-work/self-v2ray/binance.announcement/get.announcement.js:460:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[2025-08-26 06:54:29.604] [INFO] Cookie refresh started
[2025-08-26 06:54:30.851] [INFO] 🔄 Graceful shutdown initiated
[2025-08-26 06:55:53.625] [INFO] Test 1 completed successfully {"cookieLength":356,"duration":3602,"successfulStrategies":{}}
[2025-08-26 06:55:53.625] [INFO] Test 2: Cookie format validation
[2025-08-26 06:55:53.625] [WARN] Test 2 warning: Cookie quality may be insufficient {"hasAwsWafToken":true,"hasBncUuid":false,"hasSessionId":false,"qualityScore":1}
[2025-08-26 06:55:53.625] [INFO] Test 3: Cookie effectiveness validation
[2025-08-26 06:55:57.090] [INFO] Test 3 completed successfully {"isValid":true,"validationDuration":3465}
[2025-08-26 06:55:57.090] [INFO] Test 4: Strategy performance analysis
[2025-08-26 06:55:57.091] [INFO] Test 4 completed {"successfulStrategies":{},"failedStrategies":[]}
[2025-08-26 06:55:57.091] [INFO] All Playwright Cookie Manager tests completed successfully
[2025-08-26 06:55:57.091] [INFO] Test cleanup completed
[2025-08-26 06:55:57.091] [INFO] All Playwright tests completed successfully
[2025-08-26 06:56:07.709] [INFO] Cookie refresh completed successfully {"refreshCount":1,"cookieLength":356}
[2025-08-26 06:56:07.709] [INFO] Monitor started successfully
[2025-08-26 06:56:08.041] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:56:08.041] [ERROR] Monitor error {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:56:08.041] [INFO] Cookie refresh started
[2025-08-26 06:56:11.489] [INFO] Cookie refresh completed successfully {"refreshCount":2,"cookieLength":356}
[2025-08-26 06:56:11.828] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:56:11.829] [ERROR] Monitor error {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:56:12.814] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:56:12.814] [ERROR] Monitor error {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:56:12.814] [INFO] Cookie refresh started
[2025-08-26 06:56:16.334] [INFO] Cookie refresh completed successfully {"refreshCount":3,"cookieLength":356}
[2025-08-26 06:56:16.623] [INFO] Cookie expiration detected {"error":"Could not find __APP_DATA script tag - Cookie may be expired","parseStep":"processResults"}
[2025-08-26 06:56:16.623] [ERROR] Monitor error {"error":"Could not find __APP_DATA script tag - Cookie may be expired"}
[2025-08-26 06:56:17.712] [INFO] Monitor test completed successfully
