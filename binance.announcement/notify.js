require('dotenv/config')
const notifier = require('node-notifier');
const path = require('path');

const notify = (title, message) => {
  notifier.notify(
    {
      title,
      message,
      sound: true,
      wait: false
    },
    function (err, response, metadata) {
      // Response is response from notification
      // Metadata contains activationType, activationAt, deliveredAt
    }
  );
};
const sendWechatNotify = async (title, desc) => {
  if (!process.env.SERVER_CHAN) {
    console.error('no SERVER_CHAN key, setup first!');
    return;
  }
  const apiUrl = `https://sctapi.ftqq.com/${ process.env.SERVER_CHAN }.send?title=${ title }&desp=${ desc }`;
  const response = await fetch(apiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  const data = await response.text();
  console.log(`sendWechatNotify response: `, data);
};
module.exports = {
  sendWechatNotify,
  notify
};
