const { logger } = require('./logger');
const { BinanceAnnouncementMonitor } = require('./get.announcement');

/**
 * 基础功能测试（不依赖 Puppeteer）
 */
async function testBasicFunctionality() {
  console.log('🧪 开始基础功能测试...');
  logger.info('Starting basic functionality test');

  try {
    // 测试 1: 日志记录功能
    console.log('\n📋 测试 1: 日志记录功能');
    logger.info('Test 1: Logger functionality test');
    logger.warn('This is a warning message');
    logger.error('This is an error message');
    logger.debug('This is a debug message');
    
    console.log('✅ 日志记录功能测试通过');

    // 测试 2: 监控器初始化
    console.log('\n📋 测试 2: 监控器初始化');
    logger.info('Test 2: Monitor initialization test');
    
    const monitor = new BinanceAnnouncementMonitor({
      interval: 10000, // 10秒间隔
      timeout: 3000,
      concurrency: 1,
      cookieRefreshInterval: 30000 // 30秒刷新间隔
    });

    console.log('✅ 监控器初始化成功');
    logger.info('Monitor initialized successfully');

    // 测试 3: 事件监听器
    console.log('\n📋 测试 3: 事件监听器');
    logger.info('Test 3: Event listeners test');
    
    let eventReceived = false;
    
    monitor.on('started', () => {
      console.log('✅ 监控器启动事件接收成功');
      eventReceived = true;
    });

    monitor.on('error', (error) => {
      console.log(`⚠️  监控器错误事件: ${error.message}`);
      logger.error('Monitor error event received', { error: error.message });
    });

    monitor.on('cookieRefreshed', (info) => {
      console.log(`🍪 Cookie 刷新事件: 第${info.refreshCount}次`);
      logger.info('Cookie refresh event received', info);
    });

    // 测试 4: 解析功能（使用模拟数据）
    console.log('\n📋 测试 4: 解析功能');
    logger.info('Test 4: Parsing functionality test');
    
    const mockHtml = `
      <html>
        <head>
          <script id="__APP_DATA">
            {
              "appState": {
                "loader": {
                  "dataByRouteId": {
                    "route1": {
                      "latestArticles": [
                        {
                          "id": "test-1",
                          "code": "TEST001",
                          "title": "Test Announcement 1",
                          "catalogName": "General",
                          "publishDate": 1640995200000
                        },
                        {
                          "id": "test-2", 
                          "code": "TEST002",
                          "title": "Test New Cryptocurrency Listing",
                          "catalogName": "New Cryptocurrency Listing",
                          "publishDate": 1640995200000
                        }
                      ]
                    }
                  }
                }
              }
            }
          </script>
        </head>
        <body></body>
      </html>
    `;

    try {
      const parseResult = await monitor.parseAnnouncements(mockHtml);
      console.log(`✅ 解析成功: ${parseResult.latestArticles.length} 个公告, ${parseResult.listings.length} 个 Listing`);
      logger.info('Parsing test successful', {
        totalArticles: parseResult.latestArticles.length,
        listings: parseResult.listings.length
      });
    } catch (error) {
      console.log(`❌ 解析测试失败: ${error.message}`);
      logger.error('Parsing test failed', { error: error.message });
    }

    // 测试 5: 错误处理
    console.log('\n📋 测试 5: 错误处理');
    logger.info('Test 5: Error handling test');
    
    const invalidHtml = '<html><body>No __APP_DATA here</body></html>';
    
    try {
      await monitor.parseAnnouncements(invalidHtml);
      console.log('❌ 错误处理测试失败: 应该抛出错误');
    } catch (error) {
      if (error.type === 'COOKIE_EXPIRED') {
        console.log('✅ 错误处理测试通过: 正确检测到 Cookie 过期');
        logger.info('Error handling test passed: Cookie expiration detected');
      } else {
        console.log(`⚠️  错误处理测试: 收到其他错误 - ${error.message}`);
        logger.warn('Error handling test: Other error received', { error: error.message });
      }
    }

    // 测试 6: 统计功能
    console.log('\n📋 测试 6: 统计功能');
    logger.info('Test 6: Statistics functionality test');
    
    monitor.updateStats(100, true);
    monitor.updateStats(150, true);
    monitor.updateStats(200, false);
    
    const stats = monitor.getStats();
    console.log(`✅ 统计功能测试: 总请求 ${stats.totalRequests}, 成功 ${stats.successRequests}, 失败 ${stats.failedRequests}`);
    logger.info('Statistics test completed', stats);

    // 清理
    await monitor.stop();
    console.log('\n🎉 基础功能测试完成!');
    logger.info('Basic functionality test completed successfully');

  } catch (error) {
    console.error('❌ 基础功能测试失败:', error.message);
    logger.error('Basic functionality test failed', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * 测试现有 Cookie 验证功能
 */
async function testExistingCookie() {
  console.log('\n🧪 测试现有 Cookie 功能...');
  logger.info('Testing existing cookie functionality');

  try {
    const fs = require('fs').promises;
    const path = require('path');
    
    // 检查是否存在 cookie 文件
    const cookieFile = path.join(__dirname, 'cookie.txt');
    
    try {
      const cookieContent = await fs.readFile(cookieFile, 'utf8');
      console.log(`📄 找到现有 Cookie 文件，长度: ${cookieContent.length} 字符`);
      
      // 简单验证 cookie 格式
      const cookieParts = cookieContent.split(';');
      console.log(`🍪 Cookie 包含 ${cookieParts.length} 个部分`);
      
      const hasAwsWafToken = cookieContent.includes('aws-waf-token=');
      const hasBncUuid = cookieContent.includes('bnc-uuid=');
      
      console.log(`🔍 包含 aws-waf-token: ${hasAwsWafToken}`);
      console.log(`🔍 包含 bnc-uuid: ${hasBncUuid}`);
      
      if (hasAwsWafToken && hasBncUuid) {
        console.log('✅ 现有 Cookie 格式看起来正常');
        logger.info('Existing cookie format appears valid', {
          cookieLength: cookieContent.length,
          cookieParts: cookieParts.length,
          hasAwsWafToken,
          hasBncUuid
        });
      } else {
        console.log('⚠️  现有 Cookie 可能缺少关键部分');
        logger.warn('Existing cookie may be missing key parts', {
          hasAwsWafToken,
          hasBncUuid
        });
      }
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log('📄 未找到现有 Cookie 文件');
        logger.info('No existing cookie file found');
      } else {
        throw error;
      }
    }
    
  } catch (error) {
    console.error('❌ Cookie 测试失败:', error.message);
    logger.error('Cookie test failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

// 主测试函数
async function runBasicTests() {
  console.log('🎯 开始运行基础测试...\n');
  logger.info('Starting basic tests');

  try {
    await testBasicFunctionality();
    await testExistingCookie();

    console.log('\n🎉 所有基础测试成功完成!');
    logger.info('All basic tests completed successfully');

  } catch (error) {
    console.error('\n💥 基础测试失败:', error.message);
    logger.error('Basic tests failed', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  } finally {
    // 清理日志
    try {
      await logger.cleanup();
      console.log('📝 日志清理完成');
    } catch (error) {
      console.error('日志清理失败:', error.message);
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runBasicTests().catch(console.error);
}

module.exports = {
  testBasicFunctionality,
  testExistingCookie,
  runBasicTests
};
