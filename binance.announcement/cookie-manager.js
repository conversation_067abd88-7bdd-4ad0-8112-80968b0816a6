const fs = require('fs').promises;
const path = require('path');
const puppeteer = require('puppeteer');
const puppeteerExtra = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { chromium } = require('playwright');
const { chromium: playwrightExtra } = require('playwright-extra');
const playwrightStealth = require('playwright-extra-plugin-stealth');

// 配置 Puppeteer Stealth
puppeteerExtra.use(StealthPlugin());

// 配置 Playwright Stealth
playwrightExtra.use(playwrightStealth());

/**
 * 强化版 Cookie 管理器类
 * 使用多种策略确保 Cookie 获取成功
 */
class CookieManager {
  constructor(options = {}) {
    this.config = {
      cookieFile: options.cookieFile || path.join(__dirname, 'cookie.txt'),
      targetUrl: options.targetUrl || 'https://www.binance.com/en/support/announcement',
      timeout: options.timeout || 45000,
      retryAttempts: options.retryAttempts || 5,
      retryDelay: options.retryDelay || 3000,
      headless: options.headless !== false,
      strategies: options.strategies || ['puppeteer-stealth', 'playwright-stealth', 'puppeteer-basic', 'playwright-basic'],
      userAgents: options.userAgents || [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
      ],
      ...options
    };

    this.browser = null;
    this.page = null;
    this.context = null;
    this.lastCookieUpdate = null;
    this.currentStrategy = null;
    this.successfulStrategies = new Map(); // 记录成功的策略
  }

  /**
   * 获取随机用户代理
   */
  getRandomUserAgent() {
    const userAgents = this.config.userAgents;
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  /**
   * 获取浏览器启动参数
   */
  getBrowserArgs() {
    return [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-blink-features=AutomationControlled',
      '--disable-extensions',
      '--disable-plugins',
      '--disable-images',
      '--disable-javascript',
      '--window-size=1920,1080',
      '--user-agent=' + this.getRandomUserAgent()
    ];
  }

  /**
   * 使用 Puppeteer Stealth 策略
   */
  async initPuppeteerStealth() {
    console.log('🥷 使用 Puppeteer Stealth 策略...');

    this.browser = await puppeteerExtra.launch({
      headless: this.config.headless,
      args: this.getBrowserArgs(),
      defaultViewport: {
        width: 1920,
        height: 1080
      }
    });

    this.currentStrategy = 'puppeteer-stealth';
    console.log('✅ Puppeteer Stealth 浏览器启动成功');
  }

  /**
   * 使用 Playwright Stealth 策略
   */
  async initPlaywrightStealth() {
    console.log('🎭 使用 Playwright Stealth 策略...');

    this.browser = await playwrightExtra.launch({
      headless: this.config.headless,
      args: this.getBrowserArgs()
    });

    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent: this.getRandomUserAgent()
    });

    this.currentStrategy = 'playwright-stealth';
    console.log('✅ Playwright Stealth 浏览器启动成功');
  }

  /**
   * 使用基础 Puppeteer 策略
   */
  async initPuppeteerBasic() {
    console.log('🔧 使用基础 Puppeteer 策略...');

    this.browser = await puppeteer.launch({
      headless: this.config.headless,
      args: this.getBrowserArgs(),
      defaultViewport: {
        width: 1920,
        height: 1080
      }
    });

    this.currentStrategy = 'puppeteer-basic';
    console.log('✅ 基础 Puppeteer 浏览器启动成功');
  }

  /**
   * 使用基础 Playwright 策略
   */
  async initPlaywrightBasic() {
    console.log('🎪 使用基础 Playwright 策略...');

    this.browser = await chromium.launch({
      headless: this.config.headless,
      args: this.getBrowserArgs()
    });

    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent: this.getRandomUserAgent()
    });

    this.currentStrategy = 'playwright-basic';
    console.log('✅ 基础 Playwright 浏览器启动成功');
  }

  /**
   * 创建新页面并设置基本配置
   */
  async createPage() {
    if (!this.browser) {
      await this.initBrowser();
    }

    this.page = await this.browser.newPage();
    
    // 设置用户代理
    await this.page.setUserAgent(this.config.userAgent);
    
    // 设置额外的请求头
    await this.page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none'
    });

    // 设置超时
    this.page.setDefaultTimeout(this.config.timeout);
    this.page.setDefaultNavigationTimeout(this.config.timeout);

    console.log('✅ 页面创建成功');
    return this.page;
  }

  /**
   * 访问目标页面并获取 cookie
   */
  async fetchCookies() {
    let attempt = 0;
    let lastError = null;

    while (attempt < this.config.retryAttempts) {
      attempt++;
      console.log(`🔄 第 ${attempt} 次尝试获取 cookie...`);

      try {
        if (!this.page) {
          await this.createPage();
        }

        console.log(`📡 访问页面: ${this.config.targetUrl}`);
        
        // 访问目标页面
        const response = await this.page.goto(this.config.targetUrl, {
          waitUntil: 'networkidle2',
          timeout: this.config.timeout
        });

        if (!response.ok()) {
          throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
        }

        console.log('✅ 页面加载成功');

        // 等待页面完全加载
        await this.page.waitForTimeout(2000);

        // 检查是否存在 __APP_DATA 标签（验证页面是否正确加载）
        const hasAppData = await this.page.evaluate(() => {
          return document.querySelector('script#__APP_DATA') !== null;
        });

        if (!hasAppData) {
          console.log('⚠️  页面可能需要额外的等待时间或交互');
          await this.page.waitForTimeout(3000);
        }

        // 获取所有 cookie
        const cookies = await this.page.cookies();
        
        if (cookies.length === 0) {
          throw new Error('未获取到任何 cookie');
        }

        // 格式化 cookie 字符串
        const cookieString = cookies
          .map(cookie => `${cookie.name}=${cookie.value}`)
          .join('; ');

        console.log(`✅ 成功获取 ${cookies.length} 个 cookie`);
        
        // 保存 cookie 到文件
        await this.saveCookies(cookieString);
        
        this.lastCookieUpdate = Date.now();
        return cookieString;

      } catch (error) {
        lastError = error;
        console.error(`❌ 第 ${attempt} 次尝试失败:`, error.message);
        
        if (attempt < this.config.retryAttempts) {
          console.log(`⏳ ${this.config.retryDelay / 1000} 秒后重试...`);
          await this.delay(this.config.retryDelay);
          
          // 关闭当前页面，准备重试
          if (this.page) {
            await this.page.close().catch(() => {});
            this.page = null;
          }
        }
      }
    }

    throw new Error(`获取 cookie 失败，已重试 ${this.config.retryAttempts} 次。最后错误: ${lastError?.message}`);
  }

  /**
   * 保存 cookie 到文件
   */
  async saveCookies(cookieString) {
    try {
      await fs.writeFile(this.config.cookieFile, cookieString, 'utf8');
      console.log(`💾 Cookie 已保存到: ${this.config.cookieFile}`);
    } catch (error) {
      console.error('❌ 保存 cookie 失败:', error.message);
      throw new Error(`保存 cookie 失败: ${error.message}`);
    }
  }

  /**
   * 从文件读取 cookie
   */
  async loadCookies() {
    try {
      const cookieString = await fs.readFile(this.config.cookieFile, 'utf8');
      return cookieString.trim();
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log('📄 Cookie 文件不存在，需要重新获取');
        return null;
      }
      throw error;
    }
  }

  /**
   * 验证 cookie 是否有效
   */
  async validateCookies(cookieString) {
    if (!cookieString) {
      return false;
    }

    try {
      // 创建临时页面进行验证
      const testPage = await this.browser.newPage();
      
      // 解析并设置 cookie
      const cookies = this.parseCookieString(cookieString);
      await this.browser.setCookie(...cookies);
      
      // 访问目标页面
      const response = await testPage.goto(this.config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: 15000
      });

      // 检查是否能找到 __APP_DATA 标签
      const hasAppData = await testPage.evaluate(() => {
        return document.querySelector('script#__APP_DATA') !== null;
      });

      await testPage.close();
      
      const isValid = response.ok() && hasAppData;
      console.log(`🔍 Cookie 验证结果: ${isValid ? '有效' : '无效'}`);
      
      return isValid;
    } catch (error) {
      console.error('❌ Cookie 验证失败:', error.message);
      return false;
    }
  }

  /**
   * 解析 cookie 字符串为 Puppeteer 格式
   */
  parseCookieString(cookieString) {
    return cookieString.split(';').map(cookie => {
      const [name, value] = cookie.trim().split('=');
      return {
        name: name.trim(),
        value: value ? value.trim() : '',
        domain: '.binance.com',
        path: '/'
      };
    }).filter(cookie => cookie.name && cookie.value);
  }

  /**
   * 获取有效的 cookie（自动检查和刷新）
   */
  async getValidCookies() {
    console.log('🔄 开始获取有效 cookie...');
    
    // 首先尝试从文件加载现有 cookie
    let cookieString = await this.loadCookies();
    
    if (cookieString) {
      console.log('📄 找到现有 cookie，正在验证...');
      
      // 初始化浏览器用于验证
      await this.initBrowser();
      
      const isValid = await this.validateCookies(cookieString);
      if (isValid) {
        console.log('✅ 现有 cookie 有效');
        return cookieString;
      }
      
      console.log('❌ 现有 cookie 无效，需要重新获取');
    }
    
    // 获取新的 cookie
    console.log('🔄 开始获取新的 cookie...');
    await this.initBrowser();
    cookieString = await this.fetchCookies();
    
    return cookieString;
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }
      
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
      
      console.log('🧹 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error.message);
    }
  }

  /**
   * 获取 cookie 更新时间
   */
  getLastUpdateTime() {
    return this.lastCookieUpdate;
  }
}

module.exports = { CookieManager };
