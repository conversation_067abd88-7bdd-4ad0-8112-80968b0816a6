const fs = require('fs').promises;
const path = require('path');
const { chromium, firefox, webkit } = require('playwright');
const { chromium: chromiumExtra } = require('playwright-extra');
const stealthPlugin = require('puppeteer-extra-plugin-stealth');

// 配置 Playwright Stealth
chromiumExtra.use(stealthPlugin());

/**
 * 强化版 Cookie 管理器类 - 纯 Playwright 实现
 * 使用多种浏览器和策略确保 Cookie 获取成功
 */
class CookieManager {
  constructor(options = {}) {
    this.config = {
      cookieFile: options.cookieFile || path.join(__dirname, 'cookie.txt'),
      targetUrl: options.targetUrl || 'https://www.binance.com/en/support/announcement',
      timeout: options.timeout || 60000,
      retryAttempts: options.retryAttempts || 8,
      retryDelay: options.retryDelay || 2000,
      headless: options.headless !== false,
      // 多种策略：stealth + 不同浏览器
      strategies: options.strategies || [
        'chromium-stealth',
        'firefox-stealth',
        'webkit-stealth',
        'chromium-basic',
        'firefox-basic',
        'webkit-basic'
      ],
      userAgents: options.userAgents || [
        // Chrome 用户代理
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        // Firefox 用户代理
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
        // Safari 用户代理
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
      ],
      ...options
    };

    this.browser = null;
    this.page = null;
    this.context = null;
    this.lastCookieUpdate = null;
    this.currentStrategy = null;
    this.successfulStrategies = new Map(); // 记录成功的策略和成功率
    this.failedStrategies = new Set(); // 记录失败的策略
  }

  /**
   * 获取随机用户代理
   */
  getRandomUserAgent() {
    const userAgents = this.config.userAgents;
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  /**
   * 获取浏览器启动参数
   */
  getBrowserArgs() {
    return [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-blink-features=AutomationControlled',
      '--disable-extensions',
      '--disable-plugins',
      '--window-size=1920,1080'
    ];
  }

  /**
   * 根据策略初始化浏览器
   */
  async initBrowserByStrategy(strategy) {
    console.log(`🚀 使用策略: ${strategy}`);

    const [browserType, mode] = strategy.split('-');
    const isStealthMode = mode === 'stealth';

    try {
      if (isStealthMode && browserType === 'chromium') {
        // Chromium Stealth 模式
        this.browser = await chromiumExtra.launch({
          headless: this.config.headless,
          args: this.getBrowserArgs()
        });
      } else {
        // 基础模式或其他浏览器
        let browserEngine;
        switch (browserType) {
          case 'chromium':
            browserEngine = chromium;
            break;
          case 'firefox':
            browserEngine = firefox;
            break;
          case 'webkit':
            browserEngine = webkit;
            break;
          default:
            throw new Error(`未知的浏览器类型: ${browserType}`);
        }

        this.browser = await browserEngine.launch({
          headless: this.config.headless,
          args: this.getBrowserArgs()
        });
      }

      // 创建上下文
      this.context = await this.browser.newContext({
        viewport: { width: 1920, height: 1080 },
        userAgent: this.getRandomUserAgent(),
        // 额外的反检测设置
        extraHTTPHeaders: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Upgrade-Insecure-Requests': '1'
        },
        // 模拟真实设备
        deviceScaleFactor: 1,
        isMobile: false,
        hasTouch: false,
        // 地理位置（可选）
        locale: 'en-US',
        timezoneId: 'America/New_York'
      });

      this.currentStrategy = strategy;
      console.log(`✅ ${strategy} 浏览器启动成功`);

    } catch (error) {
      console.error(`❌ ${strategy} 浏览器启动失败:`, error.message);
      throw error;
    }
  }

  /**
   * 创建新页面并设置基本配置
   */
  async createPage() {
    if (!this.context) {
      throw new Error('Browser context not initialized');
    }

    this.page = await this.context.newPage();

    // 设置超时
    this.page.setDefaultTimeout(this.config.timeout);
    this.page.setDefaultNavigationTimeout(this.config.timeout);

    // 添加额外的反检测措施
    await this.page.addInitScript(() => {
      // 移除 webdriver 属性
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // 伪造 plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // 伪造 languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });

      // 移除自动化相关属性
      delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
      delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
      delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    });

    console.log('✅ 页面创建成功');
    return this.page;
  }

  /**
   * 使用单一策略获取 Cookie
   */
  async fetchCookiesWithStrategy(strategy) {
    console.log(`🎯 尝试策略: ${strategy}`);

    try {
      // 初始化浏览器
      await this.initBrowserByStrategy(strategy);

      // 创建页面
      await this.createPage();

      console.log(`📡 访问页面: ${this.config.targetUrl}`);

      // 访问目标页面，使用更宽松的等待条件
      const response = await this.page.goto(this.config.targetUrl, {
        waitUntil: 'domcontentloaded', // 改为更快的等待条件
        timeout: this.config.timeout
      });

      console.log(`📊 响应状态: ${response.status()}`);

      // 等待页面稳定
      await this.page.waitForTimeout(3000);

      // 尝试滚动页面，模拟真实用户行为
      await this.page.evaluate(() => {
        window.scrollTo(0, 100);
      });

      await this.page.waitForTimeout(1000);

      // 检查页面内容
      const pageContent = await this.page.content();
      const hasAppData = pageContent.includes('__APP_DATA');
      const hasAnnouncement = pageContent.includes('announcement') || pageContent.includes('Announcement');

      console.log(`🔍 页面检查: __APP_DATA=${hasAppData}, 公告内容=${hasAnnouncement}`);

      // 获取所有 cookie
      const cookies = await this.context.cookies();

      if (cookies.length === 0) {
        throw new Error('未获取到任何 cookie');
      }

      // 检查关键 cookie
      const hasAwsWafToken = cookies.some(c => c.name.includes('aws-waf-token'));
      const hasBncUuid = cookies.some(c => c.name.includes('bnc-uuid'));

      console.log(`🍪 Cookie 检查: aws-waf-token=${hasAwsWafToken}, bnc-uuid=${hasBncUuid}, 总数=${cookies.length}`);

      // 格式化 cookie 字符串
      const cookieString = cookies
        .map(cookie => `${cookie.name}=${cookie.value}`)
        .join('; ');

      console.log(`✅ 策略 ${strategy} 成功获取 ${cookies.length} 个 cookie`);

      // 记录成功的策略
      const successCount = this.successfulStrategies.get(strategy) || 0;
      this.successfulStrategies.set(strategy, successCount + 1);

      return cookieString;

    } catch (error) {
      console.error(`❌ 策略 ${strategy} 失败:`, error.message);
      this.failedStrategies.add(strategy);
      throw error;
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  /**
   * 使用多种策略获取 Cookie，确保成功
   */
  async fetchCookies() {
    console.log('🚀 开始多策略 Cookie 获取...');

    // 按成功率排序策略
    const sortedStrategies = [...this.config.strategies].sort((a, b) => {
      const aSuccess = this.successfulStrategies.get(a) || 0;
      const bSuccess = this.successfulStrategies.get(b) || 0;
      return bSuccess - aSuccess; // 成功次数多的优先
    });

    console.log(`📋 策略顺序: ${sortedStrategies.join(' → ')}`);

    let lastError = null;

    for (const strategy of sortedStrategies) {
      // 跳过最近失败的策略
      if (this.failedStrategies.has(strategy)) {
        console.log(`⏭️  跳过失败策略: ${strategy}`);
        continue;
      }

      try {
        const cookieString = await this.fetchCookiesWithStrategy(strategy);

        // 保存成功获取的 cookie
        await this.saveCookies(cookieString);
        this.lastCookieUpdate = Date.now();

        console.log(`🎉 Cookie 获取成功！使用策略: ${strategy}`);
        return cookieString;

      } catch (error) {
        lastError = error;
        console.log(`⚠️  策略 ${strategy} 失败，尝试下一个...`);

        // 短暂延迟后尝试下一个策略
        await this.delay(this.config.retryDelay);
      }
    }

    // 所有策略都失败了，清空失败记录重试一次
    console.log('🔄 所有策略失败，清空失败记录重试...');
    this.failedStrategies.clear();

    // 最后一次尝试：使用最可靠的策略
    const fallbackStrategy = 'chromium-stealth';
    try {
      const cookieString = await this.fetchCookiesWithStrategy(fallbackStrategy);
      await this.saveCookies(cookieString);
      this.lastCookieUpdate = Date.now();

      console.log(`🎉 备用策略成功！使用策略: ${fallbackStrategy}`);
      return cookieString;

    } catch (error) {
      lastError = error;
    }

    throw new Error(`所有策略均失败！最后错误: ${lastError?.message}`);
  }

  /**
   * 保存 cookie 到文件
   */
  async saveCookies(cookieString) {
    try {
      await fs.writeFile(this.config.cookieFile, cookieString, 'utf8');
      console.log(`💾 Cookie 已保存到: ${this.config.cookieFile}`);
    } catch (error) {
      console.error('❌ 保存 cookie 失败:', error.message);
      throw new Error(`保存 cookie 失败: ${error.message}`);
    }
  }

  /**
   * 从文件读取 cookie
   */
  async loadCookies() {
    try {
      const cookieString = await fs.readFile(this.config.cookieFile, 'utf8');
      return cookieString.trim();
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log('📄 Cookie 文件不存在，需要重新获取');
        return null;
      }
      throw error;
    }
  }

  /**
   * 验证 cookie 是否有效
   */
  async validateCookies(cookieString) {
    if (!cookieString) {
      return false;
    }

    console.log('🔍 开始验证 Cookie 有效性...');

    try {
      // 使用最可靠的策略进行验证
      await this.initBrowserByStrategy('chromium-stealth');

      // 解析并设置 cookie
      const cookies = this.parseCookieString(cookieString);
      await this.context.addCookies(cookies);

      // 创建测试页面
      const testPage = await this.context.newPage();

      // 访问目标页面
      const response = await testPage.goto(this.config.targetUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 20000
      });

      // 等待页面加载
      await testPage.waitForTimeout(2000);

      // 检查页面内容
      const pageContent = await testPage.content();
      const hasAppData = pageContent.includes('__APP_DATA');
      const hasAnnouncement = pageContent.includes('announcement') || pageContent.includes('Announcement');

      await testPage.close();

      const isValid = response.ok() && (hasAppData || hasAnnouncement);
      console.log(`🔍 Cookie 验证结果: ${isValid ? '有效' : '无效'} (状态码: ${response.status()}, __APP_DATA: ${hasAppData}, 公告内容: ${hasAnnouncement})`);

      return isValid;
    } catch (error) {
      console.error('❌ Cookie 验证失败:', error.message);
      return false;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 解析 cookie 字符串为 Playwright 格式
   */
  parseCookieString(cookieString) {
    if (!cookieString || cookieString.trim() === '') {
      return [];
    }

    // 处理单个 cookie 或多个 cookie
    const cookieParts = cookieString.includes(';') ? cookieString.split(';') : [cookieString];

    return cookieParts.map(cookie => {
      const trimmedCookie = cookie.trim();
      const equalIndex = trimmedCookie.indexOf('=');

      if (equalIndex === -1) return null;

      const name = trimmedCookie.substring(0, equalIndex).trim();
      const value = trimmedCookie.substring(equalIndex + 1).trim();

      if (!name || !value) return null;

      return {
        name: name,
        value: value,
        url: 'https://www.binance.com'
      };
    }).filter(cookie => cookie !== null);
  }

  /**
   * 获取有效的 cookie（自动检查和刷新）
   */
  async getValidCookies() {
    console.log('🔄 开始获取有效 cookie...');

    // 首先尝试从文件加载现有 cookie
    let cookieString = await this.loadCookies();

    if (cookieString) {
      console.log('📄 找到现有 cookie，正在验证...');

      const isValid = await this.validateCookies(cookieString);
      if (isValid) {
        console.log('✅ 现有 cookie 有效');
        return cookieString;
      }

      console.log('❌ 现有 cookie 无效，需要重新获取');
    }

    // 获取新的 cookie
    console.log('🔄 开始获取新的 cookie...');
    cookieString = await this.fetchCookies();

    return cookieString;
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.page) {
        await this.page.close().catch(() => {});
        this.page = null;
      }

      if (this.context) {
        await this.context.close().catch(() => {});
        this.context = null;
      }

      if (this.browser) {
        await this.browser.close().catch(() => {});
        this.browser = null;
      }

      console.log('🧹 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error.message);
    }
  }

  /**
   * 获取 cookie 更新时间
   */
  getLastUpdateTime() {
    return this.lastCookieUpdate;
  }
}

module.exports = { CookieManager };
