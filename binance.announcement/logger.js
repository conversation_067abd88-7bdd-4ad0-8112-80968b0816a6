const fs = require('fs').promises;
const path = require('path');

/**
 * 简单但功能完整的日志记录器
 */
class Logger {
  constructor(options = {}) {
    this.config = {
      logDir: options.logDir || path.join(__dirname, 'logs'),
      logLevel: options.logLevel || 'info',
      maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
      maxFiles: options.maxFiles || 5,
      enableConsole: options.enableConsole !== false,
      enableFile: options.enableFile !== false,
      dateFormat: options.dateFormat || 'YYYY-MM-DD HH:mm:ss',
      ...options
    };

    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    };

    this.currentLogFile = null;
    this.logQueue = [];
    this.isWriting = false;

    this.init();
  }

  async init() {
    if (this.config.enableFile) {
      try {
        await fs.mkdir(this.config.logDir, { recursive: true });
        this.currentLogFile = this.getLogFileName();
      } catch (error) {
        console.error('Failed to initialize logger:', error.message);
      }
    }
  }

  getLogFileName() {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
    return path.join(this.config.logDir, `binance-monitor-${dateStr}.log`);
  }

  formatTimestamp() {
    const now = new Date();
    return now.toISOString().replace('T', ' ').replace('Z', '');
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = this.formatTimestamp();
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  shouldLog(level) {
    return this.levels[level] <= this.levels[this.config.logLevel];
  }

  async writeToFile(formattedMessage) {
    if (!this.config.enableFile || !this.currentLogFile) {
      return;
    }

    this.logQueue.push(formattedMessage);

    if (this.isWriting) {
      return;
    }

    this.isWriting = true;

    try {
      while (this.logQueue.length > 0) {
        const messages = this.logQueue.splice(0, 100); // 批量写入
        const content = messages.join('\n') + '\n';
        
        await fs.appendFile(this.currentLogFile, content, 'utf8');
        
        // 检查文件大小，如果超过限制则轮转
        await this.rotateLogIfNeeded();
      }
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    } finally {
      this.isWriting = false;
    }
  }

  async rotateLogIfNeeded() {
    try {
      const stats = await fs.stat(this.currentLogFile);
      if (stats.size > this.config.maxFileSize) {
        await this.rotateLog();
      }
    } catch (error) {
      // 文件可能不存在，忽略错误
    }
  }

  async rotateLog() {
    try {
      const logFiles = await this.getLogFiles();
      
      // 删除超过限制的旧文件
      if (logFiles.length >= this.config.maxFiles) {
        const filesToDelete = logFiles.slice(0, logFiles.length - this.config.maxFiles + 1);
        for (const file of filesToDelete) {
          await fs.unlink(path.join(this.config.logDir, file));
        }
      }

      // 创建新的日志文件
      this.currentLogFile = this.getLogFileName();
    } catch (error) {
      console.error('Failed to rotate log:', error.message);
    }
  }

  async getLogFiles() {
    try {
      const files = await fs.readdir(this.config.logDir);
      return files
        .filter(file => file.startsWith('binance-monitor-') && file.endsWith('.log'))
        .sort();
    } catch (error) {
      return [];
    }
  }

  log(level, message, meta = {}) {
    if (!this.shouldLog(level)) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message, meta);

    // 控制台输出
    if (this.config.enableConsole) {
      const consoleMethod = level === 'error' ? 'error' : 
                           level === 'warn' ? 'warn' : 'log';
      console[consoleMethod](formattedMessage);
    }

    // 文件输出
    if (this.config.enableFile) {
      this.writeToFile(formattedMessage);
    }
  }

  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  trace(message, meta = {}) {
    this.log('trace', message, meta);
  }

  // 特殊方法：记录请求统计
  logStats(stats) {
    this.info('Monitor Statistics', {
      totalRequests: stats.totalRequests,
      successRequests: stats.successRequests,
      failedRequests: stats.failedRequests,
      successRate: stats.totalRequests > 0 ? 
        ((stats.successRequests / stats.totalRequests) * 100).toFixed(2) + '%' : '0%',
      avgResponseTime: stats.avgResponseTime.toFixed(2) + 'ms',
      cookieRefreshCount: stats.cookieRefreshCount,
      lastUpdateTime: stats.lastUpdateTime ? new Date(stats.lastUpdateTime).toISOString() : null
    });
  }

  // 特殊方法：记录新公告
  logNewAnnouncements(articles, type = 'general') {
    this.info(`New ${type} announcements detected`, {
      count: articles.length,
      articles: articles.map(article => ({
        id: article.id,
        title: article.title,
        catalogName: article.catalogName,
        publishDate: new Date(article.publishDate).toISOString()
      }))
    });
  }

  // 特殊方法：记录 Cookie 事件
  logCookieEvent(event, details = {}) {
    const eventMessages = {
      'refresh_start': 'Cookie refresh started',
      'refresh_success': 'Cookie refresh completed successfully',
      'refresh_failed': 'Cookie refresh failed',
      'expired_detected': 'Cookie expiration detected',
      'validation_failed': 'Cookie validation failed'
    };

    const message = eventMessages[event] || `Cookie event: ${event}`;
    const level = event.includes('failed') ? 'error' : 'info';
    
    this.log(level, message, details);
  }

  // 清理方法
  async cleanup() {
    // 确保所有待写入的日志都被写入
    while (this.logQueue.length > 0 && this.isWriting) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (this.logQueue.length > 0) {
      this.isWriting = true;
      try {
        const content = this.logQueue.join('\n') + '\n';
        if (this.currentLogFile) {
          await fs.appendFile(this.currentLogFile, content, 'utf8');
        }
        this.logQueue = [];
      } catch (error) {
        console.error('Failed to write remaining logs:', error.message);
      } finally {
        this.isWriting = false;
      }
    }
  }
}

// 创建默认日志实例
const defaultLogger = new Logger({
  logLevel: process.env.LOG_LEVEL || 'info',
  enableConsole: true,
  enableFile: true
});

module.exports = {
  Logger,
  logger: defaultLogger
};
