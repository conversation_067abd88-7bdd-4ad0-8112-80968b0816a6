const fs = require('fs').promises;
const path = require('path');
const zlib = require('zlib');
const https = require('https');
const { EventEmitter } = require('events');
const { notify, sendWechatNotify } = require('./notify');

const is429Exit = process.argv.find(arg => arg.includes('429'));
const readCookie = async () => fs.readFile(path.join(__dirname, 'cookie.txt'), 'utf8');

const isTooManyError = (error) => {
  return error?.message?.includes('Too Many Requests');
};
class RateLimiter {
  constructor() {
    this.tooManyCount = 0; // 重试计数器
    this.baseDelay = 30_000; // 初始退避时间，单位为毫秒（30秒）
    this.maxDelay = 600_000; // 最大退避时间，单位为毫秒（600秒）
    this.multiplier = 2; // 指数增长因子
    this.jitterMax = 10_000; // 最大随机抖动，单位为毫秒
    this.exitCount = 10;
    this.isLimiting = false;
  }

  // 计算退避时间
  getBackoffDelay() {
    this.tooManyCount++;
    // 计算指数退避时间：baseDelay * (multiplier ^ tooManyCount)
    let delay = this.baseDelay * Math.pow(this.multiplier, this.tooManyCount);
    // 添加随机抖动
    const jitter = Math.random() * this.jitterMax;
    // 限制最大退避时间
    delay = Math.min(delay + jitter, this.maxDelay);
    return delay;
  }

  // 重置计数器（在成功请求后调用）
  resetBackoff() {
    this.tooManyCount = 0;
  }

  // 模拟异步等待
  async wait() {
    if (this.tooManyCount > this.exitCount) {
      console.log(`[${ this.tooManyCount }]Too many 429 errors, exit program`);
      process.exit(1);
    }
    const delay = this.getBackoffDelay();
    console.log(`[${ new Date().toISOString() }]Retrying after ${ delay / 1000 }s due to 429 error (attempt ${ this.tooManyCount })`);
    this.isLimiting = true;
    await new Promise(resolve => setTimeout(resolve, delay));
    this.isLimiting = false;
  }
}
class BinanceAnnouncementMonitor extends EventEmitter {
  constructor(options = {}) {
    super();

    // 配置参数
    this.config = {
      interval: options.interval || 50, // 50ms 极速轮询
      maxRetries: options.maxRetries || 3,
      timeout: options.timeout || 2000, // 2秒超时
      concurrency: options.concurrency || 5, // 并发请求数
      enableCache: options.enableCache || true,
      enableWorkers: options.enableWorkers || false,
      ...options
    };

    this.rateLimiter = new RateLimiter();
    // 状态管理
    this.isRunning = false;
    this.lastKnownArticles = new Map();
    this.requestQueue = [];
    this.activeRequests = 0;
    this.stats = {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      lastUpdateTime: null
    };

    // HTTP Agent 复用连接
    this.agent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 1000,
      maxSockets: this.config.concurrency * 2,
      maxFreeSockets: this.config.concurrency,
      timeout: this.config.timeout
    });

    // 预编译正则表达式
    this.regexCache = {
      scriptTag: /<script\s+id="__APP_DATA"[^>]*>(.*?)<\/script>/s,
      jsonExtract: /^[\s\S]*?({[\s\S]*})[\s\S]*$/
    };

    this.init();
  }

  init() {
    // 预热连接
    this.warmupConnections();

    // 错误处理
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.emit('error', error);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection:', reason);
      this.emit('error', reason);
    });
  }

  async warmupConnections() {
    // 预热 HTTP 连接池
    const warmupPromises = [];
    for (let i = 0; i < this.config.concurrency; i++) {
      warmupPromises.push(this.makeRequest().catch(() => { }));
    }
    await Promise.allSettled(warmupPromises);
  }

  async makeRequest() {
    const cookie = await readCookie();
    const startTime = performance.now();

    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'www.binance.com',
        port: 443,
        path: '/en/support/announcement',
        method: 'GET',
        agent: this.agent,
        timeout: this.config.timeout,
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          "cookie": cookie,
          "Referer": "https://www.binance.com/en/support/announcement"
        }
      };

      const req = https.request(options, (res) => {
        let data = [];

        if (res.statusCode === 429) {
          console.log(`statusCode: ${ res.statusCode }, statusMessage: ${ res.statusMessage }`,);
          if (is429Exit) {
            this.printStats();
            notify(`429 Error!`, `Too many 429 errors, exit program`);
            process.exit(1);
          }
          // TODO: 处理 429 错误
          reject(new Error(res.statusMessage));
          return;
        }
        // 根据 Content-Encoding 创建解压缩流
        if (res.headers['content-encoding'] === 'gzip') {
          res = res.pipe(zlib.createGunzip());
        } else if (res.headers['content-encoding'] === 'deflate') {
          res = res.pipe(zlib.createInflate());
        } else if (res.headers['content-encoding'] === 'br') {
          res = res.pipe(zlib.createBrotliDecompress());
        }

        res.on('data', (chunk) => {
          data.push(chunk);
        });

        res.on('end', () => {
          const endTime = performance.now();
          const responseTime = endTime - startTime;

          this.updateStats(responseTime, true);
          const responseData = Buffer.concat(data).toString();
          resolve({ data: responseData, responseTime });
        });
      });

      req.on('error', (error) => {
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        this.updateStats(responseTime, false);
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }

  updateStats(responseTime, success) {
    this.stats.totalRequests++;
    if (success) {
      this.stats.successRequests++;
    } else {
      this.stats.failedRequests++;
    }

    // 计算平均响应时间 (移动平均)
    this.stats.avgResponseTime = this.stats.avgResponseTime === 0
      ? responseTime
      : (this.stats.avgResponseTime * 0.9 + responseTime * 0.1);
  }

  async parseAnnouncements(html) {
    try {
      // 极速解析，避免使用 cheerio
      const match = html.match(this.regexCache.scriptTag);
      if (!match) {
        throw new Error('Could not find __APP_DATA script tag');
      }

      const jsonStr = match[1];
      const data = JSON.parse(jsonStr);

      const dataByRouteId = data?.appState?.loader?.dataByRouteId;
      if (!dataByRouteId) {
        throw new Error('Invalid data structure');
      }

      const latestArticles = [];
      const listings = [];

      // 使用 Object.values 替代 for...in 循环
      Object.values(dataByRouteId).forEach(keyProp => {
        if (keyProp?.latestArticles) {
          latestArticles.push(...keyProp.latestArticles);

          // 过滤 Listing 公告
          const listingItems = keyProp.latestArticles.filter(
            item => item.catalogName === 'New Cryptocurrency Listing'
          );
          listings.push(...listingItems);
        }
      });

      return { latestArticles, listings };
    } catch (error) {
      throw new Error(`Parse error: ${ error.message }`);
      // TODO: 如果找不到 __APP_DATA script tag，则说明 cookie 过期，需要重新获取 cookie
    }
  }

  detectNewAnnouncements(articles, type = 'all') {
    const newArticles = [];
    const currentTime = Date.now();

    articles.forEach(article => {
      const key = `${ article.id }_${ article.code }`;

      if (!this.lastKnownArticles.has(key)) {
        newArticles.push({
          ...article,
          detectedAt: currentTime,
          type: type
        });
        this.lastKnownArticles.set(key, article);
      }
    });

    return newArticles;
  }
  findKeyWordsAlert(articles) {
    const findAlert = articles.filter(filterArticle);
    if (findAlert.length > 0) {
      this.emit('keyWordsAlert', findAlert);
    }
  }
  async saveAnnouncements(articles) {
    await fs.writeFile(path.resolve(__dirname, 'announcement.json'), JSON.stringify(articles, null, 2));
    // console.log(`save ${ articles.length } articles to announcement.json`);
  }
  async processResults(html, startTime) {

    try {
      const { latestArticles, listings } = await this.parseAnnouncements(html);

      await this.saveAnnouncements(latestArticles);
      // 检测新公告
      const newArticles = this.detectNewAnnouncements(latestArticles, 'general');
      const newListings = this.detectNewAnnouncements(listings, 'listing');

      this.findKeyWordsAlert(latestArticles);
      const processTime = performance.now() - startTime;

      // 发出事件
      if (newArticles.length > 0) {
        this.emit('newAnnouncements', newArticles);
      }

      if (newListings.length > 0) {
        this.emit('newListings', newListings);
        // 紧急事件，最高优先级
        this.emit('urgentAlert', {
          type: 'NEW_LISTING',
          data: newListings,
          timestamp: Date.now()
        });
      }
      if (!newArticles.length && !newListings.length) {
        console.log(`[${ new Date().toISOString() }]No new announcements `);
      }
      this.stats.lastUpdateTime = Date.now();

      return {
        success: true,
        newArticles: newArticles.length,
        newListings: newListings.length,
        processTime
      };

    } catch (error) {
      this.emit('error', error);
      return { success: false, error: error.message };
    }
  }

  async executeRequest() {
    if (this.activeRequests >= this.config.concurrency) {
      return;
    }
    this.activeRequests++;
    try {
      const startTime = performance.now();
      const result = await this.makeRequest();
      await this.processResults(result.data, startTime);
      this.rateLimiter.resetBackoff();
    } catch (error) {
      if (isTooManyError(error)) {
        await this.rateLimiter.wait();
      }
      console.error('Request failed:', error.message);
    } finally {
      this.activeRequests--;
    }
  }

  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    console.log(`🚀 Starting Binance Monitor - Interval: ${ this.config.interval }ms`);

    // 立即执行第一次请求
    setImmediate(() => this.executeRequest());

    // 高频轮询
    this.intervalId = setInterval(() => {
      if (this.activeRequests < this.config.concurrency) {
        this.executeRequest();
      }
    }, this.config.interval);

    // 状态报告
    this.statsInterval = setInterval(() => {
      this.printStats();
    }, 10000); // 每10秒报告一次状态

    this.emit('started');
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    console.log('🛑 Binance Monitor stopped');
    this.emit('stopped');
  }

  printStats() {
    if (this.rateLimiter.isLimiting) return;
    const successRate = this.stats.totalRequests > 0
      ? (this.stats.successRequests / this.stats.totalRequests * 100).toFixed(2)
      : 0;

    console.log(`📊 Stats: Total: ${ this.stats.totalRequests } | Success: ${ successRate }% | Avg Response: ${ this.stats.avgResponseTime.toFixed(2) }ms | Active: ${ this.activeRequests }`);
  }

  getStats() {
    return { ...this.stats };
  }
}


// 主程序
async function main() {
  const interval = process.env.INTERVAL ? parseInt(process.env.INTERVAL) : 10_000;
  const monitor = new BinanceAnnouncementMonitor({
    interval, // 默认10s 间隔，可以调整更快
    maxRetries: 3,
    timeout: 1500,
    concurrency: 1 // 并发请求数
  });

  // 监听新公告
  monitor.on('newAnnouncements', (articles) => {
    console.log(`🔔 New Announcements (${ articles.length }):`);
    notify(`New Announcements: ${ articles.length }`, articles.map(article => article.title).join('\n'));
    articles.forEach(article => {
      console.log(`  • [${ article.catalogName }] ${ article.title } ${ new Date(article.publishDate).toISOString() }`);
    });
  });

  // 监听新 Listing（最重要）
  monitor.on('newListings', (listings) => {
    console.log(`🚨 NEW CRYPTO LISTING DETECTED! Count: ${ listings.length }`);
    listings.forEach(listing => {
      console.log(`  🎯 ${ listing.title }`);
      console.log(`  📅 Published: ${ new Date(listing.publishDate) }`);
      console.log(`  🆔 ID: ${ listing.id }`);

      // 这里可以触发交易逻辑
      // tradingBot.executeTrade(listing);
    });
  });

  // 紧急警报
  monitor.on('urgentAlert', (alert) => {
    console.log(`🚨🚨🚨 URGENT ALERT: ${ alert.type } at ${ new Date(alert.timestamp) }`);
    // 发送通知、短信、邮件等
  });

  monitor.on('keyWordsAlert', (articles) => {
    console.log(`🚨🚨🚨 KEYWORDS ALERT: ${ articles.length }`);
    const platMessage = articles.map(article => article.title).join('\n');
    notify(`🚨🚨🚨 KEYWORDS ALERT: ${ articles.length }`, platMessage);
    sendWechatNotify(`🚨🚨🚨 ${ platMessage.slice(0, 50) }:  `, platMessage);
    articles.forEach(article => {
      console.log(`  🎯 ${ article.title }`);
      console.log(`  📅 Published: ${ new Date(article.publishDate) }`);
      console.log(`  🆔 ID: ${ article.id }`);
    });
  });

  // 错误处理
  monitor.on('error', (error) => {
    console.error('❌ Monitor Error:', error.message);
  });

  // 启动监控
  monitor.start();

  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n🔄 Shutting down gracefully...');
    monitor.stop();
    process.exit(0);
  });
}

// 导出类供其他模块使用
module.exports = { BinanceAnnouncementMonitor };

const findPlasma = (article) => {
  const isPlasma = article.title.includes('Plasma');
  const latest = article.publishDate != 1755849607081;
  return isPlasma && latest;
};
const filterSlice = [findPlasma];
const filterArticle = (article) => {
  return filterSlice.some(filter => filter(article));
};
// 如果直接运行此文件，启动监控
if (require.main === module) {
  main().catch(console.error);
}