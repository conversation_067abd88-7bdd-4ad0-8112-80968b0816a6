const fs = require('fs').promises;
const path = require('path');
const zlib = require('zlib');
const https = require('https');
const { EventEmitter } = require('events');
const { notify, sendWechatNotify } = require('./notify');
const { CookieManager } = require('./cookie-manager');
const { logger } = require('./logger');

const is429Exit = process.argv.find(arg => arg.includes('429'));
const readCookie = async () => fs.readFile(path.join(__dirname, 'cookie.txt'), 'utf8');

const isTooManyError = (error) => {
  return error?.message?.includes('Too Many Requests');
};
class RateLimiter {
  constructor() {
    this.tooManyCount = 0; // 重试计数器
    this.baseDelay = 30_000; // 初始退避时间，单位为毫秒（30秒）
    this.maxDelay = 600_000; // 最大退避时间，单位为毫秒（600秒）
    this.multiplier = 2; // 指数增长因子
    this.jitterMax = 10_000; // 最大随机抖动，单位为毫秒
    this.exitCount = 10;
    this.isLimiting = false;
  }

  // 计算退避时间
  getBackoffDelay() {
    this.tooManyCount++;
    // 计算指数退避时间：baseDelay * (multiplier ^ tooManyCount)
    let delay = this.baseDelay * Math.pow(this.multiplier, this.tooManyCount);
    // 添加随机抖动
    const jitter = Math.random() * this.jitterMax;
    // 限制最大退避时间
    delay = Math.min(delay + jitter, this.maxDelay);
    return delay;
  }

  // 重置计数器（在成功请求后调用）
  resetBackoff() {
    this.tooManyCount = 0;
  }

  // 模拟异步等待
  async wait() {
    if (this.tooManyCount > this.exitCount) {
      console.log(`[${ this.tooManyCount }]Too many 429 errors, exit program`);
      process.exit(1);
    }
    const delay = this.getBackoffDelay();
    console.log(`[${ new Date().toISOString() }]Retrying after ${ delay / 1000 }s due to 429 error (attempt ${ this.tooManyCount })`);
    this.isLimiting = true;
    await new Promise(resolve => setTimeout(resolve, delay));
    this.isLimiting = false;
  }
}
class BinanceAnnouncementMonitor extends EventEmitter {
  constructor(options = {}) {
    super();

    // 配置参数
    this.config = {
      interval: options.interval || 50, // 50ms 极速轮询
      maxRetries: options.maxRetries || 3,
      timeout: options.timeout || 2000, // 2秒超时
      concurrency: options.concurrency || 5, // 并发请求数
      enableCache: options.enableCache || true,
      enableWorkers: options.enableWorkers || false,
      cookieRefreshInterval: options.cookieRefreshInterval || 3600000, // 1小时自动刷新cookie
      ...options
    };

    this.rateLimiter = new RateLimiter();
    // Cookie 管理器
    this.cookieManager = new CookieManager({
      timeout: this.config.timeout * 2, // Cookie获取超时时间更长
      retryAttempts: 3,
      retryDelay: 5000
    });

    // 状态管理
    this.isRunning = false;
    this.lastKnownArticles = new Map();
    this.requestQueue = [];
    this.activeRequests = 0;
    this.cookieRefreshTimer = null;
    this.lastCookieRefresh = null;
    this.stats = {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      lastUpdateTime: null,
      cookieRefreshCount: 0
    };

    // HTTP Agent 复用连接
    this.agent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 1000,
      maxSockets: this.config.concurrency * 2,
      maxFreeSockets: this.config.concurrency,
      timeout: this.config.timeout
    });

    // 预编译正则表达式
    this.regexCache = {
      scriptTag: /<script\s+id="__APP_DATA"[^>]*>(.*?)<\/script>/s,
      jsonExtract: /^[\s\S]*?({[\s\S]*})[\s\S]*$/
    };

    this.init();
  }

  init() {
    // 预热连接
    this.warmupConnections();

    // 错误处理
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.emit('error', error);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection:', reason);
      this.emit('error', reason);
    });
  }

  async warmupConnections() {
    // 预热 HTTP 连接池
    const warmupPromises = [];
    for (let i = 0; i < this.config.concurrency; i++) {
      warmupPromises.push(this.makeRequest().catch(() => { }));
    }
    await Promise.allSettled(warmupPromises);
  }

  async makeRequest() {
    const cookie = await readCookie();
    const startTime = performance.now();

    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'www.binance.com',
        port: 443,
        path: '/en/support/announcement',
        method: 'GET',
        agent: this.agent,
        timeout: this.config.timeout,
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          "cookie": cookie,
          "Referer": "https://www.binance.com/en/support/announcement"
        }
      };

      const req = https.request(options, (res) => {
        let data = [];

        if (res.statusCode === 429) {
          console.log(`statusCode: ${ res.statusCode }, statusMessage: ${ res.statusMessage }`,);
          if (is429Exit) {
            this.printStats();
            notify(`429 Error!`, `Too many 429 errors, exit program`);
            process.exit(1);
          }
          // TODO: 处理 429 错误
          reject(new Error(res.statusMessage));
          return;
        }
        // 根据 Content-Encoding 创建解压缩流
        if (res.headers['content-encoding'] === 'gzip') {
          res = res.pipe(zlib.createGunzip());
        } else if (res.headers['content-encoding'] === 'deflate') {
          res = res.pipe(zlib.createInflate());
        } else if (res.headers['content-encoding'] === 'br') {
          res = res.pipe(zlib.createBrotliDecompress());
        }

        res.on('data', (chunk) => {
          data.push(chunk);
        });

        res.on('end', () => {
          const endTime = performance.now();
          const responseTime = endTime - startTime;

          this.updateStats(responseTime, true);
          const responseData = Buffer.concat(data).toString();
          resolve({ data: responseData, responseTime });
        });
      });

      req.on('error', (error) => {
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        this.updateStats(responseTime, false);
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }

  updateStats(responseTime, success) {
    this.stats.totalRequests++;
    if (success) {
      this.stats.successRequests++;
    } else {
      this.stats.failedRequests++;
    }

    // 计算平均响应时间 (移动平均)
    this.stats.avgResponseTime = this.stats.avgResponseTime === 0
      ? responseTime
      : (this.stats.avgResponseTime * 0.9 + responseTime * 0.1);
  }

  async parseAnnouncements(html) {
    try {
      // 极速解析，避免使用 cheerio
      const match = html.match(this.regexCache.scriptTag);
      if (!match) {
        // Cookie 可能过期，抛出特殊错误类型
        const error = new Error('Could not find __APP_DATA script tag - Cookie may be expired');
        error.type = 'COOKIE_EXPIRED';
        throw error;
      }

      const jsonStr = match[1];
      const data = JSON.parse(jsonStr);

      const dataByRouteId = data?.appState?.loader?.dataByRouteId;
      if (!dataByRouteId) {
        // 数据结构异常，可能也是 cookie 问题
        const error = new Error('Invalid data structure - Cookie may be expired');
        error.type = 'COOKIE_EXPIRED';
        throw error;
      }

      const latestArticles = [];
      const listings = [];

      // 使用 Object.values 替代 for...in 循环
      Object.values(dataByRouteId).forEach(keyProp => {
        if (keyProp?.latestArticles) {
          latestArticles.push(...keyProp.latestArticles);

          // 过滤 Listing 公告
          const listingItems = keyProp.latestArticles.filter(
            item => item.catalogName === 'New Cryptocurrency Listing'
          );
          listings.push(...listingItems);
        }
      });

      return { latestArticles, listings };
    } catch (error) {
      // 如果是 JSON 解析错误，也可能是 cookie 问题
      if (error instanceof SyntaxError) {
        const cookieError = new Error(`JSON parse error - Cookie may be expired: ${error.message}`);
        cookieError.type = 'COOKIE_EXPIRED';
        throw cookieError;
      }

      // 保持原有错误类型
      if (!error.type) {
        error.type = 'PARSE_ERROR';
      }
      throw error;
    }
  }

  /**
   * 刷新 Cookie
   */
  async refreshCookies() {
    logger.logCookieEvent('refresh_start');
    console.log('🔄 开始刷新 Cookie...');
    try {
      const newCookie = await this.cookieManager.getValidCookies();
      this.lastCookieRefresh = Date.now();
      this.stats.cookieRefreshCount++;

      logger.logCookieEvent('refresh_success', {
        refreshCount: this.stats.cookieRefreshCount,
        cookieLength: newCookie.length
      });
      console.log('✅ Cookie 刷新成功');

      // 发出 cookie 刷新事件
      this.emit('cookieRefreshed', {
        timestamp: this.lastCookieRefresh,
        refreshCount: this.stats.cookieRefreshCount
      });

      return newCookie;
    } catch (error) {
      logger.logCookieEvent('refresh_failed', {
        error: error.message,
        stack: error.stack
      });
      console.error('❌ Cookie 刷新失败:', error.message);
      this.emit('cookieRefreshFailed', error);
      throw error;
    } finally {
      // 清理 cookie 管理器资源
      await this.cookieManager.cleanup();
    }
  }

  /**
   * 检查是否需要刷新 Cookie
   */
  shouldRefreshCookie() {
    if (!this.lastCookieRefresh) {
      return true;
    }

    const timeSinceLastRefresh = Date.now() - this.lastCookieRefresh;
    return timeSinceLastRefresh > this.config.cookieRefreshInterval;
  }

  detectNewAnnouncements(articles, type = 'all') {
    const newArticles = [];
    const currentTime = Date.now();

    articles.forEach(article => {
      const key = `${ article.id }_${ article.code }`;

      if (!this.lastKnownArticles.has(key)) {
        newArticles.push({
          ...article,
          detectedAt: currentTime,
          type: type
        });
        this.lastKnownArticles.set(key, article);
      }
    });

    return newArticles;
  }
  findKeyWordsAlert(articles) {
    const findAlert = articles.filter(filterArticle);
    if (findAlert.length > 0) {
      this.emit('keyWordsAlert', findAlert);
    }
  }
  async saveAnnouncements(articles) {
    await fs.writeFile(path.resolve(__dirname, 'announcement.json'), JSON.stringify(articles, null, 2));
    // console.log(`save ${ articles.length } articles to announcement.json`);
  }
  async processResults(html, startTime) {
    try {
      const { latestArticles, listings } = await this.parseAnnouncements(html);

      await this.saveAnnouncements(latestArticles);
      // 检测新公告
      const newArticles = this.detectNewAnnouncements(latestArticles, 'general');
      const newListings = this.detectNewAnnouncements(listings, 'listing');

      this.findKeyWordsAlert(latestArticles);
      const processTime = performance.now() - startTime;

      // 记录新公告到日志
      if (newArticles.length > 0) {
        logger.logNewAnnouncements(newArticles, 'general');
        this.emit('newAnnouncements', newArticles);
      }

      if (newListings.length > 0) {
        logger.logNewAnnouncements(newListings, 'listing');
        logger.warn('NEW CRYPTO LISTING DETECTED!', {
          count: newListings.length,
          listings: newListings.map(l => l.title)
        });
        this.emit('newListings', newListings);
        // 紧急事件，最高优先级
        this.emit('urgentAlert', {
          type: 'NEW_LISTING',
          data: newListings,
          timestamp: Date.now()
        });
      }
      if (!newArticles.length && !newListings.length) {
        logger.debug('No new announcements detected');
        console.log(`[${ new Date().toISOString() }]No new announcements `);
      }
      this.stats.lastUpdateTime = Date.now();

      return {
        success: true,
        newArticles: newArticles.length,
        newListings: newListings.length,
        processTime
      };

    } catch (error) {
      // 检查是否是 cookie 过期错误
      if (error.type === 'COOKIE_EXPIRED') {
        logger.logCookieEvent('expired_detected', {
          error: error.message,
          parseStep: 'processResults'
        });
        console.log('🔄 检测到 Cookie 过期，准备刷新...');
        this.emit('cookieExpired', error);

        // 标记需要刷新 cookie
        error.needsCookieRefresh = true;
      } else {
        logger.error('Process results failed', {
          error: error.message,
          type: error.type || 'UNKNOWN',
          stack: error.stack
        });
      }

      this.emit('error', error);
      return {
        success: false,
        error: error.message,
        needsCookieRefresh: error.needsCookieRefresh || false
      };
    }
  }

  async executeRequest() {
    if (this.activeRequests >= this.config.concurrency) {
      return;
    }
    this.activeRequests++;

    try {
      // 检查是否需要定期刷新 cookie
      if (this.shouldRefreshCookie()) {
        console.log('⏰ 定期刷新 Cookie...');
        await this.refreshCookies();
      }

      const startTime = performance.now();
      const result = await this.makeRequest();
      const processResult = await this.processResults(result.data, startTime);

      // 检查是否需要刷新 cookie
      if (processResult.needsCookieRefresh) {
        console.log('🔄 响应数据异常，尝试刷新 Cookie...');
        await this.refreshCookies();

        // 刷新 cookie 后重新尝试请求
        console.log('🔄 Cookie 刷新完成，重新发起请求...');
        const retryResult = await this.makeRequest();
        await this.processResults(retryResult.data, startTime);
      }

      this.rateLimiter.resetBackoff();
    } catch (error) {
      if (isTooManyError(error)) {
        await this.rateLimiter.wait();
      } else if (error.type === 'COOKIE_EXPIRED' || error.needsCookieRefresh) {
        // 如果是 cookie 相关错误，尝试刷新
        try {
          console.log('🔄 请求失败，可能是 Cookie 问题，尝试刷新...');
          await this.refreshCookies();
        } catch (refreshError) {
          console.error('❌ Cookie 刷新失败:', refreshError.message);
          this.emit('criticalError', {
            type: 'COOKIE_REFRESH_FAILED',
            originalError: error,
            refreshError: refreshError
          });
        }
      }
      console.error('Request failed:', error.message);
    } finally {
      this.activeRequests--;
    }
  }

  async start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    console.log(`🚀 Starting Binance Monitor - Interval: ${ this.config.interval }ms`);

    // 启动时先确保有有效的 cookie
    try {
      console.log('🔄 启动时检查 Cookie 状态...');
      await this.refreshCookies();
    } catch (error) {
      console.error('❌ 启动时 Cookie 获取失败:', error.message);
      this.emit('startupError', error);
      // 继续启动，但会在后续请求中重试
    }

    // 立即执行第一次请求
    setImmediate(() => this.executeRequest());

    // 高频轮询
    this.intervalId = setInterval(() => {
      if (this.activeRequests < this.config.concurrency) {
        this.executeRequest();
      }
    }, this.config.interval);

    // 状态报告
    this.statsInterval = setInterval(() => {
      this.printStats();
    }, 10000); // 每10秒报告一次状态

    // 定期 Cookie 刷新（可选，因为已经在 executeRequest 中检查）
    this.cookieRefreshTimer = setInterval(() => {
      if (this.shouldRefreshCookie()) {
        console.log('⏰ 定时器触发 Cookie 刷新...');
        this.refreshCookies().catch(error => {
          console.error('❌ 定时 Cookie 刷新失败:', error.message);
        });
      }
    }, this.config.cookieRefreshInterval);

    this.emit('started');
  }

  async stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    if (this.cookieRefreshTimer) {
      clearInterval(this.cookieRefreshTimer);
    }

    // 清理 cookie 管理器资源
    try {
      await this.cookieManager.cleanup();
    } catch (error) {
      console.error('❌ Cookie 管理器清理失败:', error.message);
    }

    console.log('🛑 Binance Monitor stopped');
    this.emit('stopped');
  }

  printStats() {
    if (this.rateLimiter.isLimiting) return;
    const successRate = this.stats.totalRequests > 0
      ? (this.stats.successRequests / this.stats.totalRequests * 100).toFixed(2)
      : 0;

    const cookieAge = this.lastCookieRefresh
      ? Math.floor((Date.now() - this.lastCookieRefresh) / 1000 / 60) // 分钟
      : 'N/A';

    const statsMessage = `📊 Stats: Total: ${ this.stats.totalRequests } | Success: ${ successRate }% | Avg Response: ${ this.stats.avgResponseTime.toFixed(2) }ms | Active: ${ this.activeRequests } | Cookie Refreshes: ${ this.stats.cookieRefreshCount } | Cookie Age: ${ cookieAge }min`;

    console.log(statsMessage);

    // 每隔一段时间记录详细统计到日志文件
    if (this.stats.totalRequests % 100 === 0 && this.stats.totalRequests > 0) {
      logger.logStats({
        ...this.stats,
        activeRequests: this.activeRequests,
        cookieAge: cookieAge,
        rateLimiterStatus: this.rateLimiter.isLimiting
      });
    }
  }

  getStats() {
    return { ...this.stats };
  }
}


// 主程序
async function main() {
  logger.info('🚀 Binance Announcement Monitor Starting', {
    version: '2.0.0',
    nodeVersion: process.version,
    platform: process.platform,
    pid: process.pid
  });

  const interval = process.env.INTERVAL ? parseInt(process.env.INTERVAL) : 10_000;
  const monitor = new BinanceAnnouncementMonitor({
    interval, // 默认10s 间隔，可以调整更快
    maxRetries: 3,
    timeout: 1500,
    concurrency: 1 // 并发请求数
  });

  logger.info('Monitor configuration', {
    interval,
    maxRetries: 3,
    timeout: 1500,
    concurrency: 1
  });

  // 监听新公告
  monitor.on('newAnnouncements', (articles) => {
    const message = `🔔 New Announcements (${ articles.length }):`;
    console.log(message);
    logger.info(message, { count: articles.length });

    notify(`New Announcements: ${ articles.length }`, articles.map(article => article.title).join('\n'));
    articles.forEach(article => {
      console.log(`  • [${ article.catalogName }] ${ article.title } ${ new Date(article.publishDate).toISOString() }`);
    });
  });

  // 监听新 Listing（最重要）
  monitor.on('newListings', (listings) => {
    const message = `🚨 NEW CRYPTO LISTING DETECTED! Count: ${ listings.length }`;
    console.log(message);
    logger.warn(message, {
      count: listings.length,
      listings: listings.map(l => ({ id: l.id, title: l.title }))
    });

    listings.forEach(listing => {
      console.log(`  🎯 ${ listing.title }`);
      console.log(`  📅 Published: ${ new Date(listing.publishDate) }`);
      console.log(`  🆔 ID: ${ listing.id }`);

      // 这里可以触发交易逻辑
      // tradingBot.executeTrade(listing);
    });
  });

  // 紧急警报
  monitor.on('urgentAlert', (alert) => {
    console.log(`🚨🚨🚨 URGENT ALERT: ${ alert.type } at ${ new Date(alert.timestamp) }`);
    // 发送通知、短信、邮件等
  });

  monitor.on('keyWordsAlert', (articles) => {
    console.log(`🚨🚨🚨 KEYWORDS ALERT: ${ articles.length }`);
    const platMessage = articles.map(article => article.title).join('\n');
    notify(`🚨🚨🚨 KEYWORDS ALERT: ${ articles.length }`, platMessage);
    sendWechatNotify(`🚨🚨🚨 ${ platMessage.slice(0, 50) }:  `, platMessage);
    articles.forEach(article => {
      console.log(`  🎯 ${ article.title }`);
      console.log(`  📅 Published: ${ new Date(article.publishDate) }`);
      console.log(`  🆔 ID: ${ article.id }`);
    });
  });

  // 错误处理
  monitor.on('error', (error) => {
    const message = '❌ Monitor Error:' + error.message;
    console.error(message);
    logger.error(message, {
      error: error.message,
      type: error.type || 'UNKNOWN',
      stack: error.stack
    });
  });

  // Cookie 相关事件
  monitor.on('cookieExpired', (error) => {
    const message = '🍪 Cookie 过期检测:' + error.message;
    console.log(message);
    logger.warn(message, { error: error.message });
  });

  monitor.on('cookieRefreshed', (info) => {
    const message = `🍪 Cookie 刷新成功 (第${info.refreshCount}次) - ${new Date(info.timestamp).toISOString()}`;
    console.log(message);
    logger.info(message, info);
  });

  monitor.on('cookieRefreshFailed', (error) => {
    const message = '🍪 Cookie 刷新失败:' + error.message;
    console.error(message);
    logger.error(message, {
      error: error.message,
      stack: error.stack
    });
    notify('Cookie 刷新失败', `错误: ${error.message}`);
  });

  monitor.on('criticalError', (errorInfo) => {
    const message = '🚨 严重错误:' + errorInfo.type;
    console.error(message);
    console.error('原始错误:', errorInfo.originalError?.message);
    console.error('刷新错误:', errorInfo.refreshError?.message);

    logger.error(message, {
      type: errorInfo.type,
      originalError: errorInfo.originalError?.message,
      refreshError: errorInfo.refreshError?.message,
      originalStack: errorInfo.originalError?.stack,
      refreshStack: errorInfo.refreshError?.stack
    });

    notify('严重错误', `类型: ${errorInfo.type}\n原始错误: ${errorInfo.originalError?.message}`);
  });

  monitor.on('startupError', (error) => {
    const message = '🚀 启动错误:' + error.message;
    console.error(message);
    logger.error(message, {
      error: error.message,
      stack: error.stack
    });
  });

  // 启动监控
  await monitor.start();

  // 优雅关闭
  process.on('SIGINT', async () => {
    console.log('\n🔄 Shutting down gracefully...');
    logger.info('🔄 Graceful shutdown initiated');

    try {
      await monitor.stop();
      logger.info('✅ Monitor stopped successfully');
    } catch (error) {
      const message = '❌ 关闭过程中出错:' + error.message;
      console.error(message);
      logger.error(message, { error: error.message, stack: error.stack });
    }

    // 清理日志
    try {
      await logger.cleanup();
      console.log('📝 日志清理完成');
    } catch (error) {
      console.error('❌ 日志清理失败:', error.message);
    }

    logger.info('👋 Binance Announcement Monitor Shutdown Complete');
    process.exit(0);
  });
}

// 导出类供其他模块使用
module.exports = { BinanceAnnouncementMonitor };

const findPlasma = (article) => {
  const isPlasma = article.title.includes('Plasma');
  const latest = article.publishDate != 1755849607081;
  return isPlasma && latest;
};
const filterSlice = [findPlasma];
const filterArticle = (article) => {
  return filterSlice.some(filter => filter(article));
};
// 如果直接运行此文件，启动监控
if (require.main === module) {
  main().catch(console.error);
}